package com.example.tts_truong_lai

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.speech.tts.TextToSpeech
import androidx.core.app.Person
import androidx.core.content.LocusIdCompat
import androidx.core.content.pm.ShortcutInfoCompat
import androidx.core.content.pm.ShortcutManagerCompat
import androidx.core.graphics.drawable.IconCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.util.Locale

private const val CATEGORY_TEXT_SHARE_TARGET = "com.example.android.bubbles.category.TEXT_SHARE_TARGET"

class MainActivity : FlutterActivity(){

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initState()
        ShortcutManagerCompat.reportShortcutUsed(context, "id_be_no2");
    }
    private lateinit var tts: TextToSpeech
    private val CHANNEL = "tts"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "speak") {
                val text = call.argument<String>("text")
                if (text != null) {
                    tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, null)
                    result.success(null)
                } else {
                    result.error("UNAVAILABLE", "Text not available.", null)
                }
            } else {
                result.notImplemented()
            }
        }
        tts = TextToSpeech(this) { status -> if (status == TextToSpeech.SUCCESS) tts.language = Locale("vi", "VN") }

    }

    private fun initState() {
        // Example of creating a shortcut, replace with your actual logic
        // val id = 1 // Example ID
        createShortcuts(this)
    }

    private fun createShortcuts(context: Context) {
        val CATEGORY_TEXT_SHARE_TARGET = "androidx.sharetarget.category.TEXT_SHARE_TARGET"
        val CATEGORY_CONVERSATION = "android.shortcut.conversation"

        val shortcut1 = ShortcutInfoCompat.Builder(context, "id_be_no")
            .setShortLabel("Gửi Bé Nơ")
            .setLongLabel("Gửi cho Bé Nơ")
            .setIcon(IconCompat.createWithResource(context, R.drawable.launch_background))
            .setIntent(Intent(Intent.ACTION_VIEW).apply {
                type = "text/plain"
                data = Uri.parse("content://com.example.tts_truong_lai/be_no")
            })
            .setCategories(setOf(CATEGORY_CONVERSATION, CATEGORY_TEXT_SHARE_TARGET))
            .setPerson(
                Person.Builder()
                    .setName("Bé Nơ")
                    .setImportant(true)
                    .build()
            )
            .setLongLived(true)
            .setLocusId(LocusIdCompat("id_be_no"))
            .setIsConversation()
            .build()

        val shortcut2 = ShortcutInfoCompat.Builder(context, "id_be_no2")
            .setShortLabel("Gửi Bé Nơ 2")
            .setLongLabel("Gửi cho Bé Nơ 2")
            .setIcon(IconCompat.createWithResource(context, R.drawable.launch_background))
            .setIntent(Intent(Intent.ACTION_VIEW).apply {
                type = "text/plain"
                data = Uri.parse("content://com.example.tts_truong_lai/be_no2")
            })
            .setCategories(setOf(CATEGORY_CONVERSATION, CATEGORY_TEXT_SHARE_TARGET))
            .setPerson(
                Person.Builder()
                    .setName("Bé Nơ 2")
                    .setImportant(true)
                    .build()
            )
            .setLongLived(true)
            .setLocusId(LocusIdCompat("id_be_no2"))
            .setIsConversation()
            .build()

        ShortcutManagerCompat.addDynamicShortcuts(context, listOf(shortcut1, shortcut2))

    }
}