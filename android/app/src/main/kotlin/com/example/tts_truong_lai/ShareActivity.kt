package com.example.tts_truong_lai
import android.content.Intent
import android.os.Bundle
import android.os.Build
import androidx.core.content.pm.ShortcutManagerCompat
import io.flutter.embedding.android.FlutterActivity

class ShareActivity : FlutterActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        ShortcutManagerCompat.reportShortcutUsed(context, "id_be_no");


//        when {
//            intent?.action == Intent.ACTION_SEND -> {
//                if ("text/plain" == intent.type) {
//                    intent.getStringExtra(Intent.EXTRA_TEXT)?.let {
//                        text_shared.text = it
//                    }
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P &&
//                        intent.hasExtra(Intent.EXTRA_SHORTCUT_ID)) {
//                        val shortcutId = intent.getStringExtra(Intent.EXTRA_SHORTCUT_ID)
//                        val e = ""
//                    }
//                }
//            }
//        }

    }
}