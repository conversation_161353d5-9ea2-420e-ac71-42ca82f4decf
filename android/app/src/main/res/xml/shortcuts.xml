<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">
  <share-target
    android:targetClass="com.example.tts_truong_lai.ShareActivity"
    android:targetPackage="com.example.tts_truong_lai">
    <data android:mimeType="text/plain" />
    <category android:name="com.example.SHARE_GROUP" />
  </share-target>

  <share-target
    android:targetClass="com.example.tts_truong_lai.ShareActivity1"
    android:targetPackage="com.example.tts_truong_lai">
    <data android:mimeType="text/plain" />
    <category android:name="com.example.SHARE_CALENDAR" />
  </share-target>
    <!-- <shortcut
        android:shortcutId="id_be_no"
        android:enabled="true"
        android:shortcutShortLabel="@string/shortcut_short_label_be_no"
        android:shortcutLongLabel="@string/shortcut_long_label_be_no"
        android:icon="@drawable/launch_background">

        <intent
            android:action="android.intent.action.VIEW"
            android:targetClass="com.example.tts_truong_lai.ShareActivity"
            android:targetPackage="com.example.tts_truong_lai" />
    </shortcut>

    <shortcut
        android:shortcutId="id_be_no1"
        android:enabled="true"
        android:shortcutShortLabel="@string/shortcut_short_label_be_no"
        android:shortcutLongLabel="@string/shortcut_long_label_be_no"
        android:icon="@drawable/launch_background">

        <intent
            android:action="android.intent.action.VIEW"
            android:targetClass="com.example.tts_truong_lai.ShareActivity1"
            android:targetPackage="com.example.tts_truong_lai" />
    </shortcut> -->

    <!-- Có thể khai thêm shortcut khác nữa -->
</shortcuts>
