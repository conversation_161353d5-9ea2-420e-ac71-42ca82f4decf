// pubspec.yaml dependencies:
// dependencies:
//   flutter:
//     sdk: flutter
//   http: ^1.1.0
//   audioplayers: ^5.2.1
//   path_provider: ^2.1.1
//   permission_handler: ^11.0.1

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class GoogleTTSService {
  // Thay YOUR_API_KEY bằng API key thực tế của bạn
  static const String _apiKey = 'YOUR_GOOGLE_CLOUD_API_KEY';
  static const String _baseUrl = 'https://texttospeech.googleapis.com/v1/text:synthesize';

  static Future<Uint8List?> synthesizeText(String text) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'input': {'text': text},
          'voice': {
            'languageCode': 'vi-VN',
            'name': 'vi-VN-Standard-A',
            'ssmlGender': 'FEMALE',
          },
          'audioConfig': {
            'audioEncoding': 'MP3',
            'speakingRate': 1.0,
            'pitch': 0.0,
            'volumeGainDb': 0.0,
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final audioContent = data['audioContent'] as String;
        return base64Decode(audioContent);
      } else {
        print('Lỗi API: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Lỗi gọi API: $e');
      return null;
    }
  }
}

class NumberTTSWidget extends StatefulWidget {
  @override
  _NumberTTSWidgetState createState() => _NumberTTSWidgetState();
}

class _NumberTTSWidgetState extends State<NumberTTSWidget> {
  final List<int> numbers = [345, 456, 789, 223];
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  List<Map<String, dynamic>> audioFiles = [];
  bool isLoading = false;
  bool isPlaying = false;
  int? currentPlayingIndex;

  @override
  void initState() {
    super.initState();
    _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    await Permission.storage.request();
  }

  Future<void> generateAllAudio() async {
    setState(() {
      isLoading = true;
      audioFiles.clear();
    });

    for (int i = 0; i < numbers.length; i++) {
      final number = numbers[i];
      final text = 'số báo danh $number';
      
      final audioData = await GoogleTTSService.synthesizeText(text);
      
      if (audioData != null) {
        // Lưu file tạm thời
        final tempFile = await _saveAudioFile(audioData, number);
        
        audioFiles.add({
          'number': number,
          'text': text,
          'audioData': audioData,
          'filePath': tempFile?.path,
          'isGenerated': true,
        });
      } else {
        audioFiles.add({
          'number': number,
          'text': text,
          'audioData': null,
          'filePath': null,
          'isGenerated': false,
        });
      }
      
      setState(() {});
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<File?> _saveAudioFile(Uint8List audioData, int number) async {
    try {
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/so_bao_danh_$number.mp3');
      await file.writeAsBytes(audioData);
      return file;
    } catch (e) {
      print('Lỗi lưu file: $e');
      return null;
    }
  }

  Future<void> playAudio(int index) async {
    if (audioFiles[index]['filePath'] == null) return;

    try {
      if (isPlaying && currentPlayingIndex == index) {
        await _audioPlayer.stop();
        setState(() {
          isPlaying = false;
          currentPlayingIndex = null;
        });
        return;
      }

      await _audioPlayer.stop();
      await _audioPlayer.play(DeviceFileSource(audioFiles[index]['filePath']));
      
      setState(() {
        isPlaying = true;
        currentPlayingIndex = index;
      });

      _audioPlayer.onPlayerComplete.listen((event) {
        setState(() {
          isPlaying = false;
          currentPlayingIndex = null;
        });
      });

    } catch (e) {
      print('Lỗi phát audio: $e');
    }
  }

  Future<void> playAllSequentially() async {
    for (int i = 0; i < audioFiles.length; i++) {
      if (audioFiles[i]['filePath'] != null) {
        setState(() {
          currentPlayingIndex = i;
          isPlaying = true;
        });

        await _audioPlayer.play(DeviceFileSource(audioFiles[i]['filePath']));
        
        // Chờ audio phát xong
        await _audioPlayer.onPlayerComplete.first;
        
        // Nghỉ 1 giây giữa các số
        await Future.delayed(Duration(seconds: 1));
      }
    }
    
    setState(() {
      isPlaying = false;
      currentPlayingIndex = null;
    });
  }

  Future<void> downloadAudioFiles() async {
    final directory = await getExternalStorageDirectory();
    if (directory == null) return;

    final downloadDir = Directory('${directory.path}/TTS_Audio');
    if (!await downloadDir.exists()) {
      await downloadDir.create(recursive: true);
    }

    for (final audioFile in audioFiles) {
      if (audioFile['audioData'] != null) {
        final file = File('${downloadDir.path}/so_bao_danh_${audioFile['number']}.mp3');
        await file.writeAsBytes(audioFile['audioData']);
      }
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Đã lưu ${audioFiles.length} file vào ${downloadDir.path}')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Google TTS - Số báo danh'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Thông tin danh sách số
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Danh sách số báo danh:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(numbers.join(', '), style: TextStyle(fontSize: 16)),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Buttons điều khiển
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: isLoading ? null : generateAllAudio,
                    icon: Icon(Icons.mic),
                    label: Text('Tạo Audio'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: audioFiles.isEmpty ? null : playAllSequentially,
                    icon: Icon(Icons.play_arrow),
                    label: Text('Phát tất cả'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: audioFiles.isEmpty ? null : downloadAudioFiles,
                    icon: Icon(Icons.download),
                    label: Text('Tải về'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Loading indicator
            if (isLoading)
              LinearProgressIndicator(
                value: audioFiles.length / numbers.length,
              ),
            
            SizedBox(height: 16),
            
            // Danh sách audio files
            Expanded(
              child: ListView.builder(
                itemCount: audioFiles.length,
                itemBuilder: (context, index) {
                  final audioFile = audioFiles[index];
                  final isCurrentPlaying = currentPlayingIndex == index && isPlaying;
                  
                  return Card(
                    margin: EdgeInsets.symmetric(vertical: 4),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: audioFile['isGenerated'] ? Colors.green : Colors.red,
                        child: Text(
                          audioFile['number'].toString(),
                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                      title: Text(audioFile['text']),
                      subtitle: Text(
                        audioFile['isGenerated'] ? 'Đã tạo audio' : 'Lỗi tạo audio',
                        style: TextStyle(
                          color: audioFile['isGenerated'] ? Colors.green : Colors.red,
                        ),
                      ),
                      trailing: audioFile['isGenerated']
                          ? IconButton(
                              onPressed: () => playAudio(index),
                              icon: Icon(
                                isCurrentPlaying ? Icons.stop : Icons.play_arrow,
                                color: isCurrentPlaying ? Colors.red : Colors.blue,
                              ),
                            )
                          : Icon(Icons.error, color: Colors.red),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}

// Main App
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Google TTS Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: NumberTTSWidget(),
    );
  }
}

void main() {
  runApp(MyApp());
}