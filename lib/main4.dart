import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// Flutter code sample for [showCupertinoSheet].

void main() {
  runApp(const CupertinoSheetApp());
}

class CupertinoSheetApp extends StatelessWidget {
  const CupertinoSheetApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(title: 'Cupertino Sheet', home: HomePage());
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
   
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CupertinoButton.filled(
              onPressed: () {
                showCupertinoSheet<void>(
                  context: context,
               
                  pageBuilder: (BuildContext context) => const _SheetScaffold(),
                );
              },
              child: const Text('Open Bottom Sheet'),
            ),
          ],
        ),
      ),
    );
  }
}

class _SheetScaffold extends StatelessWidget {
  const _SheetScaffold();

  @override
  Widget build(BuildContext context) {
    return const CupertinoPageScaffold(child: _SheetBody(title: 'CupertinoSheetRoute'));
  }
}

class _SheetBody extends StatelessWidget {
  const _SheetBody({required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(title),
          CupertinoButton.filled(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Go Back'),
          ),
          CupertinoButton.filled(
            onPressed: () {
              CupertinoSheetRoute.popSheet(context);
            },
            child: const Text('Pop Whole Sheet'),
          ),
          CupertinoButton.filled(
            onPressed: () {
              Navigator.of(context).push(
                CupertinoPageRoute<void>(builder: (BuildContext context) => const _SheetNextPage()),
              );
            },
            child: const Text('Push Nested Page'),
          ),
          CupertinoButton.filled(
            onPressed: () {
              showCupertinoSheet<void>(
                context: context,
                useNestedNavigation: false,
                pageBuilder: (BuildContext context) => const _SheetScaffold(),
              );
            },
            child: const Text('Push Another Sheet'),
          ),
        ],
      ),
    );
  }
}

class _SheetNextPage extends StatelessWidget {
  const _SheetNextPage();

  @override
  Widget build(BuildContext context) {
    return const CupertinoPageScaffold(
      backgroundColor: CupertinoColors.activeOrange,
      child: _SheetBody(title: 'Next Page'),
    );
  }
}